2025-08-06 00:38:55,796 - INFO - ==================================================
2025-08-06 00:38:55,798 - INFO - 数据文件合并系统启动
2025-08-06 00:38:55,799 - INFO - ==================================================
2025-08-06 00:38:55,800 - INFO - 开始扫描文件...
2025-08-06 00:38:55,800 - INFO - 数据目录: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30
2025-08-06 00:38:55,802 - INFO - 扫描平台: 抖音 (DY)
2025-08-06 00:38:55,819 - INFO - 扫描平台: 天猫 (TM)
2025-08-06 00:38:55,862 - INFO - 扫描平台: 京东 (JD)
2025-08-06 00:38:55,890 - INFO - 扫描平台: 拼多多 (PDD)
2025-08-06 00:38:55,903 - INFO - 扫描完成，共发现 81 个文件
2025-08-06 00:38:55,903 - INFO - 分为 12 个合并组
2025-08-06 00:38:55,903 - INFO - 开始多线程处理，使用 4 个线程
2025-08-06 00:38:55,906 - INFO - 处理分组: DY_数据_历史会话
2025-08-06 00:38:55,907 - INFO - 处理分组: DY_数据_客服数据
2025-08-06 00:38:55,908 - INFO - 处理分组: TM_一目了然_新客服周数据
2025-08-06 00:38:55,909 - INFO - 处理分组: TM_一目了然_新客服绩效数据
2025-08-06 00:38:56,754 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:56,852 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:56,933 - INFO - 列 'PC在线时长（时）' 转换为数字类型: float64
2025-08-06 00:38:56,956 - INFO - 列 'PC小休时长（时）' 转换为数字类型: float64
2025-08-06 00:38:56,959 - INFO - 列 'PC离线时长（时）' 转换为数字类型: float64
2025-08-06 00:38:56,992 - INFO - 抖音去掉了 2 行汇总数据
2025-08-06 00:38:57,061 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,063 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,248 - INFO - 列 'PC在线时长（时）' 转换为数字类型: float64
2025-08-06 00:38:57,259 - INFO - 列 'PC小休时长（时）' 转换为数字类型: float64
2025-08-06 00:38:57,263 - INFO - 列 'PC离线时长（时）' 转换为数字类型: float64
2025-08-06 00:38:57,288 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,293 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,295 - INFO - 抖音去掉了 2 行汇总数据
2025-08-06 00:38:57,403 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,578 - INFO - 列 'PC在线时长（时）' 转换为数字类型: float64
2025-08-06 00:38:57,599 - INFO - 列 'PC小休时长（时）' 转换为数字类型: float64
2025-08-06 00:38:57,616 - INFO - 列 'PC离线时长（时）' 转换为数字类型: float64
2025-08-06 00:38:57,616 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,662 - INFO - 抖音去掉了 2 行汇总数据
2025-08-06 00:38:57,676 - INFO - 合并 DY_数据_客服数据 组的 3 个文件
2025-08-06 00:38:57,705 - INFO - 合并完成，共 59 行数据，21 列
2025-08-06 00:38:57,726 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,981 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:57,995 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:58,231 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:58,351 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:58,490 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:58,525 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\抖音_数据_客服数据.xlsx (包含 59 行数据)
2025-08-06 00:38:58,533 - INFO - 处理分组: TM_客服绩效_付款
2025-08-06 00:38:58,753 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:58,760 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:58,766 - INFO - 合并 TM_一目了然_新客服周数据 组的 9 个文件
2025-08-06 00:38:58,939 - INFO - 合并完成，共 99 行数据，24 列
2025-08-06 00:38:58,992 - INFO - 列 '本人落实下单付款' 转换为数字类型: float64
2025-08-06 00:38:59,016 - INFO - 列 'Unnamed: 3' 转换为数字类型: float64
2025-08-06 00:38:59,027 - INFO - 列 'Unnamed: 4' 转换为数字类型: float64
2025-08-06 00:38:59,054 - INFO - 列 'Unnamed: 5' 转换为数字类型: float64
2025-08-06 00:38:59,076 - INFO - 列 '本人落实下单他人落实付款' 转换为数字类型: float64
2025-08-06 00:38:59,087 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:59,089 - INFO - 列 'Unnamed: 7' 转换为数字类型: float64
2025-08-06 00:38:59,119 - INFO - 列 'Unnamed: 8' 转换为数字类型: float64
2025-08-06 00:38:59,125 - INFO - 列 'Unnamed: 9' 转换为数字类型: float64
2025-08-06 00:38:59,151 - INFO - 列 '他人落实下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:38:59,173 - INFO - 列 'Unnamed: 11' 转换为数字类型: float64
2025-08-06 00:38:59,183 - INFO - 列 'Unnamed: 12' 转换为数字类型: float64
2025-08-06 00:38:59,193 - INFO - 列 'Unnamed: 13' 转换为数字类型: float64
2025-08-06 00:38:59,203 - INFO - 列 '静默下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:38:59,214 - INFO - 列 'Unnamed: 15' 转换为数字类型: float64
2025-08-06 00:38:59,217 - INFO - 列 'Unnamed: 16' 转换为数字类型: float64
2025-08-06 00:38:59,227 - INFO - 列 'Unnamed: 17' 转换为数字类型: float64
2025-08-06 00:38:59,245 - INFO - 列 'Unnamed: 18' 转换为数字类型: float64
2025-08-06 00:38:59,249 - INFO - 列 'Unnamed: 19' 转换为数字类型: float64
2025-08-06 00:38:59,265 - INFO - 列 'Unnamed: 20' 转换为数字类型: float64
2025-08-06 00:38:59,276 - INFO - 列 'Unnamed: 21' 转换为数字类型: float64
2025-08-06 00:38:59,280 - INFO - 列 '全静默单本人跟进' 转换为数字类型: float64
2025-08-06 00:38:59,296 - INFO - 列 'Unnamed: 23' 转换为数字类型: float64
2025-08-06 00:38:59,324 - INFO - 列 'Unnamed: 24' 转换为数字类型: float64
2025-08-06 00:38:59,335 - INFO - 列 'Unnamed: 25' 转换为数字类型: float64
2025-08-06 00:38:59,421 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:59,421 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:38:59,426 - INFO - 合并 TM_一目了然_新客服绩效数据 组的 9 个文件
2025-08-06 00:38:59,531 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\天猫_一目了然_新客服周数据.xlsx (包含 99 行数据)
2025-08-06 00:38:59,546 - INFO - 处理分组: TM_绩效明细_旺旺退款明细
2025-08-06 00:38:59,715 - INFO - 合并完成，共 99 行数据，24 列
2025-08-06 00:38:59,944 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:00,409 - INFO - 列 '本人落实下单付款' 转换为数字类型: float64
2025-08-06 00:39:00,447 - INFO - 列 'Unnamed: 3' 转换为数字类型: float64
2025-08-06 00:39:00,474 - INFO - 列 'Unnamed: 4' 转换为数字类型: float64
2025-08-06 00:39:00,478 - INFO - 列 'Unnamed: 5' 转换为数字类型: float64
2025-08-06 00:39:00,489 - INFO - 列 '本人落实下单他人落实付款' 转换为数字类型: float64
2025-08-06 00:39:00,500 - INFO - 列 'Unnamed: 7' 转换为数字类型: float64
2025-08-06 00:39:00,511 - INFO - 列 'Unnamed: 8' 转换为数字类型: float64
2025-08-06 00:39:00,534 - INFO - 列 'Unnamed: 9' 转换为数字类型: float64
2025-08-06 00:39:00,551 - INFO - 列 '他人落实下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:39:00,557 - INFO - 列 'Unnamed: 11' 转换为数字类型: float64
2025-08-06 00:39:00,590 - INFO - 列 'Unnamed: 12' 转换为数字类型: float64
2025-08-06 00:39:00,607 - INFO - 列 'Unnamed: 13' 转换为数字类型: float64
2025-08-06 00:39:00,614 - INFO - 列 '静默下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:39:00,622 - INFO - 列 'Unnamed: 15' 转换为数字类型: float64
2025-08-06 00:39:00,648 - INFO - 列 '商家编码' 转换为数字类型: float64
2025-08-06 00:39:00,657 - INFO - 列 'Unnamed: 16' 转换为数字类型: float64
2025-08-06 00:39:00,698 - INFO - 列 'Unnamed: 17' 转换为数字类型: float64
2025-08-06 00:39:00,702 - INFO - 列 'Unnamed: 18' 转换为数字类型: float64
2025-08-06 00:39:00,711 - INFO - 列 'Unnamed: 19' 转换为数字类型: float64
2025-08-06 00:39:00,722 - INFO - 列 'Unnamed: 20' 转换为数字类型: float64
2025-08-06 00:39:00,734 - INFO - 列 'Unnamed: 21' 转换为数字类型: float64
2025-08-06 00:39:00,737 - INFO - 列 '全静默单本人跟进' 转换为数字类型: float64
2025-08-06 00:39:00,747 - INFO - 列 'Unnamed: 23' 转换为数字类型: float64
2025-08-06 00:39:00,775 - INFO - 列 'Unnamed: 24' 转换为数字类型: float64
2025-08-06 00:39:00,784 - INFO - 列 'Unnamed: 25' 转换为数字类型: float64
2025-08-06 00:39:00,809 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\天猫_一目了然_新客服绩效数据.xlsx (包含 99 行数据)
2025-08-06 00:39:00,835 - INFO - 处理分组: TM_绩效明细_旺旺销售明细
2025-08-06 00:39:00,836 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:01,489 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:01,885 - INFO - 列 '本人落实下单付款' 转换为数字类型: float64
2025-08-06 00:39:01,894 - INFO - 列 'Unnamed: 3' 转换为数字类型: float64
2025-08-06 00:39:01,903 - INFO - 列 'Unnamed: 4' 转换为数字类型: float64
2025-08-06 00:39:01,930 - INFO - 列 'Unnamed: 5' 转换为数字类型: float64
2025-08-06 00:39:01,939 - INFO - 列 '本人落实下单他人落实付款' 转换为数字类型: float64
2025-08-06 00:39:01,945 - INFO - 列 'Unnamed: 7' 转换为数字类型: float64
2025-08-06 00:39:01,955 - INFO - 列 'Unnamed: 8' 转换为数字类型: float64
2025-08-06 00:39:01,986 - INFO - 列 'Unnamed: 9' 转换为数字类型: float64
2025-08-06 00:39:02,020 - INFO - 列 '他人落实下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:39:02,039 - INFO - 列 'Unnamed: 11' 转换为数字类型: float64
2025-08-06 00:39:02,051 - INFO - 列 'Unnamed: 12' 转换为数字类型: float64
2025-08-06 00:39:02,076 - INFO - 列 'Unnamed: 13' 转换为数字类型: float64
2025-08-06 00:39:02,082 - INFO - 列 '静默下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:39:02,101 - INFO - 列 'Unnamed: 15' 转换为数字类型: float64
2025-08-06 00:39:02,139 - INFO - 列 'Unnamed: 16' 转换为数字类型: float64
2025-08-06 00:39:02,166 - INFO - 列 'Unnamed: 17' 转换为数字类型: float64
2025-08-06 00:39:02,214 - INFO - 列 'Unnamed: 18' 转换为数字类型: float64
2025-08-06 00:39:02,239 - INFO - 列 'Unnamed: 19' 转换为数字类型: float64
2025-08-06 00:39:02,277 - INFO - 列 'Unnamed: 20' 转换为数字类型: float64
2025-08-06 00:39:02,303 - INFO - 列 'Unnamed: 21' 转换为数字类型: float64
2025-08-06 00:39:02,306 - INFO - 列 '全静默单本人跟进' 转换为数字类型: float64
2025-08-06 00:39:02,322 - INFO - 列 'Unnamed: 23' 转换为数字类型: float64
2025-08-06 00:39:02,341 - INFO - 列 'Unnamed: 24' 转换为数字类型: float64
2025-08-06 00:39:02,348 - INFO - 列 'Unnamed: 25' 转换为数字类型: float64
2025-08-06 00:39:02,448 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:03,066 - INFO - 列 '本人落实下单付款' 转换为数字类型: float64
2025-08-06 00:39:03,085 - INFO - 列 'Unnamed: 3' 转换为数字类型: float64
2025-08-06 00:39:03,116 - INFO - 列 'Unnamed: 4' 转换为数字类型: float64
2025-08-06 00:39:03,142 - INFO - 列 'Unnamed: 5' 转换为数字类型: float64
2025-08-06 00:39:03,166 - INFO - 列 '本人落实下单他人落实付款' 转换为数字类型: float64
2025-08-06 00:39:03,198 - INFO - 列 'Unnamed: 7' 转换为数字类型: float64
2025-08-06 00:39:03,212 - INFO - 列 'Unnamed: 8' 转换为数字类型: float64
2025-08-06 00:39:03,232 - INFO - 列 'Unnamed: 9' 转换为数字类型: float64
2025-08-06 00:39:03,268 - INFO - 列 '他人落实下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:39:03,298 - INFO - 列 'Unnamed: 11' 转换为数字类型: float64
2025-08-06 00:39:03,346 - INFO - 列 'Unnamed: 12' 转换为数字类型: float64
2025-08-06 00:39:03,358 - INFO - 列 'Unnamed: 13' 转换为数字类型: float64
2025-08-06 00:39:03,371 - INFO - 列 '静默下单本人落实付款' 转换为数字类型: float64
2025-08-06 00:39:03,378 - INFO - 列 'Unnamed: 15' 转换为数字类型: float64
2025-08-06 00:39:03,403 - INFO - 列 'Unnamed: 16' 转换为数字类型: float64
2025-08-06 00:39:03,437 - INFO - 列 'Unnamed: 17' 转换为数字类型: float64
2025-08-06 00:39:03,460 - INFO - 列 'Unnamed: 18' 转换为数字类型: float64
2025-08-06 00:39:03,477 - INFO - 列 'Unnamed: 19' 转换为数字类型: float64
2025-08-06 00:39:03,486 - INFO - 列 'Unnamed: 20' 转换为数字类型: float64
2025-08-06 00:39:03,515 - INFO - 列 'Unnamed: 21' 转换为数字类型: float64
2025-08-06 00:39:03,548 - INFO - 列 '全静默单本人跟进' 转换为数字类型: float64
2025-08-06 00:39:03,563 - INFO - 列 'Unnamed: 23' 转换为数字类型: float64
2025-08-06 00:39:03,586 - INFO - 列 'Unnamed: 24' 转换为数字类型: float64
2025-08-06 00:39:03,616 - INFO - 列 'Unnamed: 25' 转换为数字类型: float64
2025-08-06 00:39:03,698 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:04,389 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:05,267 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:05,931 - INFO - 天猫去掉了 2 行汇总数据
2025-08-06 00:39:05,936 - INFO - 合并 TM_客服绩效_付款 组的 9 个文件
2025-08-06 00:39:06,029 - INFO - 合并完成，共 74 行数据，26 列
2025-08-06 00:39:06,648 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\天猫_客服绩效_付款.xlsx (包含 74 行数据)
2025-08-06 00:39:06,664 - INFO - 处理分组: JD_叮咚查询_促成订单查询
2025-08-06 00:39:22,148 - INFO - 合并 TM_绩效明细_旺旺退款明细 组的 8 个文件
2025-08-06 00:39:22,799 - INFO - 合并完成，共 4264 行数据，22 列
2025-08-06 00:39:32,943 - INFO - 合并 DY_数据_历史会话 组的 3 个文件
2025-08-06 00:39:33,310 - INFO - 合并 JD_叮咚查询_促成订单查询 组的 6 个文件
2025-08-06 00:39:33,340 - INFO - 合并完成，共 10674 行数据，28 列
2025-08-06 00:39:33,417 - INFO - 合并完成，共 9666 行数据，12 列
2025-08-06 00:39:41,493 - INFO - 合并 TM_绩效明细_旺旺销售明细 组的 9 个文件
2025-08-06 00:39:41,854 - INFO - 合并完成，共 8779 行数据，14 列
2025-08-06 00:39:49,702 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\天猫_绩效明细_旺旺退款明细.xlsx (包含 4264 行数据)
2025-08-06 00:39:49,704 - INFO - 处理分组: JD_客服数据对比_售前销售绩效对比
2025-08-06 00:39:49,746 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:49,792 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:49,835 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:49,881 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:49,926 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:49,972 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:49,973 - INFO - 合并 JD_客服数据对比_售前销售绩效对比 组的 6 个文件
2025-08-06 00:39:49,978 - INFO - 合并完成，共 45 行数据，11 列
2025-08-06 00:39:50,033 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\京东_客服数据对比_售前销售绩效对比.xlsx (包含 45 行数据)
2025-08-06 00:39:50,034 - INFO - 处理分组: JD_客服数据对比_工作量对比
2025-08-06 00:39:50,102 - INFO - 列 '回复率' 转换为数字类型: float64
2025-08-06 00:39:50,108 - INFO - 列 '30s应答率' 转换为数字类型: float64
2025-08-06 00:39:50,113 - INFO - 列 '3分钟人工回复率' 转换为数字类型: float64
2025-08-06 00:39:50,118 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:50,122 - INFO - 列 '邀评率' 转换为数字类型: float64
2025-08-06 00:39:50,128 - INFO - 列 '留言响应率' 转换为数字类型: float64
2025-08-06 00:39:50,133 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:50,136 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,199 - INFO - 列 '回复率' 转换为数字类型: float64
2025-08-06 00:39:50,203 - INFO - 列 '30s应答率' 转换为数字类型: float64
2025-08-06 00:39:50,208 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:50,212 - INFO - 列 '邀评率' 转换为数字类型: float64
2025-08-06 00:39:50,218 - INFO - 列 '留言响应率' 转换为数字类型: float64
2025-08-06 00:39:50,222 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,285 - INFO - 列 '回复率' 转换为数字类型: float64
2025-08-06 00:39:50,290 - INFO - 列 '30s应答率' 转换为数字类型: float64
2025-08-06 00:39:50,294 - INFO - 列 '3分钟人工回复率' 转换为数字类型: float64
2025-08-06 00:39:50,299 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:50,304 - INFO - 列 '邀评率' 转换为数字类型: float64
2025-08-06 00:39:50,312 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:50,316 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,380 - INFO - 列 '回复率' 转换为数字类型: float64
2025-08-06 00:39:50,385 - INFO - 列 '30s应答率' 转换为数字类型: float64
2025-08-06 00:39:50,387 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:50,390 - INFO - 列 '邀评率' 转换为数字类型: float64
2025-08-06 00:39:50,394 - INFO - 列 '留言响应率' 转换为数字类型: float64
2025-08-06 00:39:50,397 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,454 - INFO - 列 '回复率' 转换为数字类型: float64
2025-08-06 00:39:50,459 - INFO - 列 '30s应答率' 转换为数字类型: float64
2025-08-06 00:39:50,462 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:50,466 - INFO - 列 '邀评率' 转换为数字类型: float64
2025-08-06 00:39:50,474 - INFO - 列 '留言响应率' 转换为数字类型: float64
2025-08-06 00:39:50,478 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,541 - INFO - 列 '回复率' 转换为数字类型: float64
2025-08-06 00:39:50,546 - INFO - 列 '30s应答率' 转换为数字类型: float64
2025-08-06 00:39:50,550 - INFO - 列 '3分钟人工回复率' 转换为数字类型: float64
2025-08-06 00:39:50,556 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:50,560 - INFO - 列 '邀评率' 转换为数字类型: float64
2025-08-06 00:39:50,570 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:50,574 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,574 - INFO - 合并 JD_客服数据对比_工作量对比 组的 6 个文件
2025-08-06 00:39:50,588 - INFO - 合并完成，共 45 行数据，20 列
2025-08-06 00:39:50,638 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,685 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,727 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,772 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,817 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,864 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:50,866 - INFO - 合并 JD_客服数据对比_售前销售绩效对比 组的 6 个文件
2025-08-06 00:39:50,869 - INFO - 合并完成，共 45 行数据，11 列
2025-08-06 00:39:50,888 - INFO - 成功为京东工作量对比数据添加售前接待人数和促成出库人数列
2025-08-06 00:39:50,964 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\京东_客服数据对比_工作量对比.xlsx (包含 45 行数据)
2025-08-06 00:39:50,965 - INFO - 处理分组: JD_客服数据对比_满意度评价对比
2025-08-06 00:39:51,021 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:51,026 - INFO - 列 '不满意率' 转换为数字类型: float64
2025-08-06 00:39:51,032 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:51,036 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:51,099 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:51,106 - INFO - 列 '差评率' 转换为数字类型: float64
2025-08-06 00:39:51,110 - INFO - 列 '首次解决率' 转换为数字类型: float64
2025-08-06 00:39:51,116 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:51,120 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:51,171 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:51,176 - INFO - 列 '不满意率' 转换为数字类型: float64
2025-08-06 00:39:51,180 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:51,185 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:51,246 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:51,250 - INFO - 列 '差评率' 转换为数字类型: float64
2025-08-06 00:39:51,255 - INFO - 列 '首次解决率' 转换为数字类型: float64
2025-08-06 00:39:51,259 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:51,263 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:51,318 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:51,324 - INFO - 列 '差评率' 转换为数字类型: float64
2025-08-06 00:39:51,329 - INFO - 列 '首次解决率' 转换为数字类型: float64
2025-08-06 00:39:51,334 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:51,338 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:51,392 - INFO - 列 '满意率' 转换为数字类型: float64
2025-08-06 00:39:51,398 - INFO - 列 '不满意率' 转换为数字类型: float64
2025-08-06 00:39:51,403 - INFO - 列 '解决率' 转换为数字类型: float64
2025-08-06 00:39:51,407 - INFO - 京东去掉了 2 行汇总数据
2025-08-06 00:39:51,408 - INFO - 合并 JD_客服数据对比_满意度评价对比 组的 6 个文件
2025-08-06 00:39:51,417 - INFO - 合并完成，共 45 行数据，19 列
2025-08-06 00:39:51,492 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\京东_客服数据对比_满意度评价对比.xlsx (包含 45 行数据)
2025-08-06 00:39:51,492 - INFO - 处理分组: PDD_多多客服-客服数据-客服绩效数据-客服绩效详情
2025-08-06 00:39:51,509 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\京东_叮咚查询_促成订单查询.xlsx (包含 9666 行数据)
2025-08-06 00:39:51,587 - INFO - 列 '询单转化率' 转换为数字类型: float64
2025-08-06 00:39:51,601 - INFO - 列 '30秒应答率(8-23点)' 转换为数字类型: float64
2025-08-06 00:39:51,605 - INFO - 列 '评分≤3率' 转换为数字类型: float64
2025-08-06 00:39:51,611 - INFO - 列 '3分钟人工回复率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,616 - INFO - 列 '30秒应答率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,620 - INFO - 拼多多去掉了 2 行汇总数据
2025-08-06 00:39:51,724 - INFO - 列 '询单转化率' 转换为数字类型: float64
2025-08-06 00:39:51,737 - INFO - 列 '30秒应答率(8-23点)' 转换为数字类型: float64
2025-08-06 00:39:51,743 - INFO - 列 '评分≤3率' 转换为数字类型: float64
2025-08-06 00:39:51,747 - INFO - 列 '3分钟人工回复率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,751 - INFO - 列 '30秒应答率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,755 - INFO - 拼多多去掉了 2 行汇总数据
2025-08-06 00:39:51,836 - INFO - 列 '询单转化率' 转换为数字类型: float64
2025-08-06 00:39:51,844 - INFO - 列 '30秒应答率(8-23点)' 转换为数字类型: float64
2025-08-06 00:39:51,849 - INFO - 列 '评分≤3率' 转换为数字类型: float64
2025-08-06 00:39:51,854 - INFO - 列 '3分钟人工回复率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,859 - INFO - 列 '30秒应答率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,863 - INFO - 拼多多去掉了 2 行汇总数据
2025-08-06 00:39:51,951 - INFO - 列 '询单转化率' 转换为数字类型: float64
2025-08-06 00:39:51,963 - INFO - 列 '30秒应答率(8-23点)' 转换为数字类型: float64
2025-08-06 00:39:51,967 - INFO - 列 '评分≤3率' 转换为数字类型: float64
2025-08-06 00:39:51,971 - INFO - 列 '3分钟人工回复率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,977 - INFO - 列 '30秒应答率(全天)' 转换为数字类型: float64
2025-08-06 00:39:51,980 - INFO - 拼多多去掉了 2 行汇总数据
2025-08-06 00:39:52,054 - INFO - 列 '询单转化率' 转换为数字类型: float64
2025-08-06 00:39:52,065 - INFO - 列 '30秒应答率(8-23点)' 转换为数字类型: float64
2025-08-06 00:39:52,068 - INFO - 列 '评分≤3率' 转换为数字类型: float64
2025-08-06 00:39:52,072 - INFO - 列 '3分钟人工回复率(全天)' 转换为数字类型: float64
2025-08-06 00:39:52,077 - INFO - 列 '30秒应答率(全天)' 转换为数字类型: float64
2025-08-06 00:39:52,081 - INFO - 拼多多去掉了 2 行汇总数据
2025-08-06 00:39:52,165 - INFO - 列 '询单转化率' 转换为数字类型: float64
2025-08-06 00:39:52,174 - INFO - 列 '30秒应答率(8-23点)' 转换为数字类型: float64
2025-08-06 00:39:52,179 - INFO - 列 '评分≤3率' 转换为数字类型: float64
2025-08-06 00:39:52,185 - INFO - 列 '3分钟人工回复率(全天)' 转换为数字类型: float64
2025-08-06 00:39:52,190 - INFO - 列 '30秒应答率(全天)' 转换为数字类型: float64
2025-08-06 00:39:52,194 - INFO - 拼多多去掉了 2 行汇总数据
2025-08-06 00:39:52,196 - INFO - 合并 PDD_多多客服-客服数据-客服绩效数据-客服绩效详情 组的 6 个文件
2025-08-06 00:39:52,206 - INFO - 合并完成，共 76 行数据，24 列
2025-08-06 00:39:52,258 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\天猫_绩效明细_旺旺销售明细.xlsx (包含 8779 行数据)
2025-08-06 00:39:52,317 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\拼多多_多多客服-客服数据-客服绩效数据-客服绩效详情.xlsx (包含 76 行数据)
2025-08-06 00:39:52,746 - INFO - 保存合并结果: C:\Users\<USER>\Desktop\宝贝专属文件夹\合并数据\6月\（客服部）新客服绩效及数据2025-06-01_2025-06-30\合并结果\抖音_数据_历史会话.xlsx (包含 10674 行数据)
2025-08-06 00:39:52,750 - INFO - ==================================================
2025-08-06 00:39:52,752 - INFO - 数据合并完成！
2025-08-06 00:39:52,754 - INFO - 成功处理: 12 个分组
2025-08-06 00:39:52,755 - INFO - 失败处理: 0 个分组
2025-08-06 00:39:52,756 - INFO - ==================================================
