#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据汇总系统 - 生产级版本
整合数据合并和汇总功能，提供图形化用户界面
"""

import os
import re
import pandas as pd
import numpy as np
from pathlib import Path
from collections import defaultdict
import threading
from datetime import datetime
import warnings
from concurrent.futures import ThreadPoolExecutor, as_completed
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import queue
from openpyxl import load_workbook, Workbook
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.styles import NamedStyle, Font, Alignment
from openpyxl.worksheet.formula import ArrayFormula

# 忽略pandas警告
warnings.filterwarnings('ignore')

class DataSummarySystem:
    """数据汇总系统主类"""
    
    def __init__(self):
        """初始化系统"""
        self.root = tk.Tk()
        self.root.title("数据汇总系统 v1.0")
        self.root.geometry("900x700")
        
        # 路径变量
        self.source_folder = tk.StringVar()
        self.summary_file = tk.StringVar()
        self.budget_file = tk.StringVar()

        # 阶段选择变量（默认都勾选）
        self.stage_shop_name_enabled = tk.BooleanVar(value=True)  # 店铺名标准化阶段
        self.stage0_enabled = tk.BooleanVar(value=True)  # 阶段0：源数据合并
        self.stage1_enabled = tk.BooleanVar(value=True)  # 阶段1：客服指标汇总表处理
        self.stage2_enabled = tk.BooleanVar(value=True)  # 阶段2：客服业绩预算表处理

        # 进度相关
        self.progress_queue = queue.Queue()
        self.is_processing = False
        self.stop_processing = False  # 停止处理标志
        
        # 平台配置
        self.platforms = ['DY', 'TM', 'JD', 'PDD']
        self.platform_names = {
            'DY': '抖音',
            'TM': '天猫',
            'JD': '京东',
            'PDD': '拼多多'
        }
        
        # 数据映射配置
        self.setup_data_mapping()
        
        # 创建GUI
        self.create_gui()
        
        # 启动进度更新
        self.update_progress()
        
    def setup_data_mapping(self):
        """设置数据映射配置"""
        # 店铺名标准化映射配置
        self.shop_name_mapping = {
            'DY': {
                '爱墨打印机专营店': '爱墨小标3C数码旗舰店（抖音）',
                '印先森打印机': 'MR.IN打印机旗舰店（抖音）',
                '印先森官方旗舰店': '小in爱学习（抖音）'
            },
            'JD': {
                '爱墨官方旗舰店': '爱墨官方旗舰店（京东）',
                '爱墨京东自营旗舰店': '爱墨白营店（京东）',
                '雅柯莱京东自营旗舰店': '雅柯莱白营店（京东）',
                '雅柯莱旗舰店': '雅柯莱（京东）',
                '印先森（MR.IN）京东自营旗舰店': '印先森MRIN白营店（京东）',
                '印先森旗舰店': '印先森MRIN旗舰店（京东）'
            },
            'PDD': {
                '爱墨官方旗舰店': '爱墨官方旗舰店（拼多多新）',
                '行盛数码专营店': '凌傲数码专营店（拼多多）',
                '奇优萌旗舰店': '奇优萌旗舰店（拼多多）',
                '小标办公用品旗舰店': '小标办公旗舰店（拼多多）',
                '雅柯莱办公用品旗舰店': '雅柯莱办公用品旗舰店（拼多多）',
                '印先森旗舰店': '印先森旗舰店（拼多多）'
            },
            'TM': {
                '爱墨旗舰店': '爱墨旗舰店（天猫）',
                '凌标办公专营店': '凌标办公专营店（天猫）',
                '奇优萌旗舰店': '奇优萌旗舰店（天猫）',
                '享打就打旗舰店': '享打就打旗舰店（天猫）',
                '小标旗舰店': '小标旗舰店（天猫）',
                '雅柯莱旗舰店': '雅柯莱旗舰店（天猫）',
                '印先森做基专卖店': '印先森做基专卖店（天猫）',
                '印先森行盛专卖店': '印先森行盛专卖店（天猫）',
                '印先森旗舰店': '印先森旗舰店（天猫）'
            }
        }

        # 阶段1：客服指标汇总表数据源映射
        self.stage1_file_mapping = {
            '抖音': '抖音_数据_客服数据.xlsx',
            '拼多多': '拼多多_多多客服-客服数据-客服绩效数据-客服绩效详情.xlsx',
            '天猫': '天猫_一目了然_新客服绩效数据.xlsx',
            '京东': '京东_客服数据对比_工作量对比.xlsx'
        }
        
        # 阶段1：字段映射规则
        self.stage1_field_mapping = {
            '年月': {
                '拼多多': '时间',
                '抖音': '时间',
                '天猫': '时间',
                '京东': '时间'
            },
            '店铺': {
                '拼多多': '店铺',
                '抖音': '店铺',
                '天猫': '店铺',
                '京东': '店铺'
            },
            '客服昵称': {
                '拼多多': '客服账号',
                '抖音': '客服昵称',
                '天猫': '旺旺',
                '京东': '客服'
            },
            '平均响应时长': {
                '拼多多': '平均人工响应时长2',
                '抖音': '新平均响应时长（秒）',
                '天猫': '平均响应(秒)',
                '京东': '平均响应时间'
            },
            '满意率': {
                '拼多多': None,
                '抖音': '满意率',
                '天猫': '客户满意率',
                '京东': '满意率'
            },
            '询单人数': {
                '拼多多': '询单人数',
                '抖音': '询单人数',
                '天猫': '询单人数',
                '京东': '售前接待人数'
            },
            '销售人数': {
                '拼多多': '最终成团人数',
                '抖音': '支付人数',
                '天猫': '询单->最终付款人数',
                '京东': '促成出库人数'
            }
        }
        
        # 阶段2：客服业绩预算表数据源映射
        self.stage2_file_mapping = {
            '拼多多': '拼多多_多多客服-客服数据-客服绩效数据-客服绩效详情.xlsx',
            '抖音': '抖音_数据_客服数据.xlsx',
            '京东': '京东_叮咚查询_促成订单查询.xlsx',
            '天猫销售': '天猫_绩效明细_旺旺销售明细.xlsx',
            '天猫退款': '天猫_绩效明细_旺旺退款明细.xlsx'
        }
        
    def create_gui(self):
        """创建图形用户界面"""
        # 设置样式
        style = ttk.Style()
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="数据汇总系统", style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=3, pady=10)
        
        # 源数据文件夹选择
        ttk.Label(main_frame, text="源数据文件夹:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        source_entry = ttk.Entry(main_frame, textvariable=self.source_folder, width=80)
        source_entry.grid(row=1, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(main_frame, text="浏览", command=self.browse_source_folder).grid(row=1, column=2, padx=5, pady=5)

        # 客服指标汇总表选择
        ttk.Label(main_frame, text="客服指标汇总表:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        summary_entry = ttk.Entry(main_frame, textvariable=self.summary_file, width=80)
        summary_entry.grid(row=2, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(main_frame, text="浏览", command=self.browse_summary_file).grid(row=2, column=2, padx=5, pady=5)

        # 客服业绩预算表选择
        ttk.Label(main_frame, text="客服业绩预算表:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        budget_entry = ttk.Entry(main_frame, textvariable=self.budget_file, width=80)
        budget_entry.grid(row=3, column=1, padx=5, pady=5, sticky=(tk.W, tk.E))
        ttk.Button(main_frame, text="浏览", command=self.browse_budget_file).grid(row=3, column=2, padx=5, pady=5)

        # 阶段选择区域
        stage_frame = ttk.LabelFrame(main_frame, text="执行阶段选择", padding="5")
        stage_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), padx=5, pady=10)

        ttk.Checkbutton(stage_frame, text="店铺名标准化", variable=self.stage_shop_name_enabled).grid(row=0, column=0, sticky=tk.W, padx=10, pady=2)
        ttk.Checkbutton(stage_frame, text="阶段0：源数据合并", variable=self.stage0_enabled).grid(row=0, column=1, sticky=tk.W, padx=10, pady=2)
        ttk.Checkbutton(stage_frame, text="阶段1：客服指标汇总表处理", variable=self.stage1_enabled).grid(row=1, column=0, sticky=tk.W, padx=10, pady=2)
        ttk.Checkbutton(stage_frame, text="阶段2：客服业绩预算表处理", variable=self.stage2_enabled).grid(row=1, column=1, sticky=tk.W, padx=10, pady=2)

        # 进度条
        ttk.Label(main_frame, text="处理进度:").grid(row=5, column=0, sticky=tk.W, padx=5, pady=10)
        self.progress_bar = ttk.Progressbar(main_frame, mode='determinate', length=700)
        self.progress_bar.grid(row=5, column=1, columnspan=2, padx=5, pady=10, sticky=(tk.W, tk.E))
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), padx=5, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=100, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 配置日志颜色标签
        self.log_text.tag_config("INFO", foreground="black")
        self.log_text.tag_config("SUCCESS", foreground="green")
        self.log_text.tag_config("WARNING", foreground="orange")
        self.log_text.tag_config("ERROR", foreground="red")

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, columnspan=3, pady=10)

        self.process_button = ttk.Button(button_frame, text="开始处理", command=self.start_processing)
        self.process_button.pack(side=tk.LEFT, padx=5)

        self.stop_button = ttk.Button(button_frame, text="停止处理", command=self.stop_processing_func, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="清理日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="退出", command=self.root.quit).pack(side=tk.LEFT, padx=5)

        # 配置行列权重
        main_frame.rowconfigure(6, weight=1)
        main_frame.columnconfigure(1, weight=1)  # 让第二列（文件路径列）可以拉伸
        
    def browse_source_folder(self):
        """浏览选择源数据文件夹"""
        folder = filedialog.askdirectory(title="选择源数据文件夹")
        if folder:
            self.source_folder.set(folder)
            self.log("已选择源数据文件夹: " + folder)
            
    def browse_summary_file(self):
        """浏览选择客服指标汇总表文件"""
        file = filedialog.askopenfilename(
            title="选择客服指标汇总表",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if file:
            self.summary_file.set(file)
            self.log("已选择客服指标汇总表: " + os.path.basename(file))
            
    def browse_budget_file(self):
        """浏览选择客服业绩预算表文件"""
        file = filedialog.askopenfilename(
            title="选择客服业绩预算表",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if file:
            self.budget_file.set(file)
            self.log("已选择客服业绩预算表: " + os.path.basename(file))

    def clear_log(self):
        """清理日志内容"""
        if messagebox.askyesno("确认", "确定要清理所有日志内容吗？"):
            self.log_text.delete(1.0, tk.END)

    def stop_processing_func(self):
        """停止处理"""
        if messagebox.askyesno("确认", "确定要停止当前处理吗？"):
            self.stop_processing = True
            self.log("用户请求停止处理...", "WARNING")
            
    def log(self, message, level="INFO"):
        """添加日志消息到GUI"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {level}: {message}\n"
        self.progress_queue.put(("log", (log_message, level)))
        
    def update_progress(self):
        """更新进度条和日志"""
        try:
            while not self.progress_queue.empty():
                msg_type, msg_data = self.progress_queue.get_nowait()

                if msg_type == "log":
                    if isinstance(msg_data, tuple):
                        log_message, level = msg_data
                        self.log_text.insert(tk.END, log_message, level)
                    else:
                        self.log_text.insert(tk.END, msg_data)
                    self.log_text.see(tk.END)
                elif msg_type == "progress":
                    self.progress_bar['value'] = msg_data
                elif msg_type == "max_progress":
                    self.progress_bar['maximum'] = msg_data

        except queue.Empty:
            pass

        self.root.after(100, self.update_progress)
        
    def start_processing(self):
        """开始处理数据"""
        # 验证输入
        if not self.source_folder.get():
            messagebox.showerror("错误", "请选择源数据文件夹")
            return

        # 检查是否至少选择了一个阶段
        if not (self.stage_shop_name_enabled.get() or self.stage0_enabled.get() or self.stage1_enabled.get() or self.stage2_enabled.get()):
            messagebox.showerror("错误", "请至少选择一个执行阶段")
            return

        # 如果选择了阶段1或阶段2，需要相应的目标文件
        if self.stage1_enabled.get() and not self.summary_file.get():
            messagebox.showerror("错误", "选择了阶段1，请选择客服指标汇总表文件")
            return

        if self.stage2_enabled.get() and not self.budget_file.get():
            messagebox.showerror("错误", "选择了阶段2，请选择客服业绩预算表文件")
            return
            
        # 重置停止标志
        self.stop_processing = False

        # 更新按钮状态
        self.process_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.is_processing = True

        # 在新线程中执行处理
        thread = threading.Thread(target=self.process_data)
        thread.start()
        
    def process_data(self):
        """主处理流程"""
        try:
            self.log("开始数据处理...")
            merged_files = None

            # 店铺名标准化阶段（如果勾选）
            if self.stage_shop_name_enabled.get():
                if self.stop_processing:
                    self.log("处理已被用户停止", "WARNING")
                    return

                self.log("=== 店铺名标准化阶段 ===")
                success = self.standardize_shop_names()

                if not success:
                    self.log("店铺名标准化失败", "ERROR")
                    # 不返回，允许继续执行其他阶段
            else:
                self.log("跳过店铺名标准化阶段")

            # 阶段0: 数据合并（如果勾选）
            if self.stage0_enabled.get():
                if self.stop_processing:
                    self.log("处理已被用户停止", "WARNING")
                    return

                self.log("=== 阶段0: 源数据合并 ===")
                merged_files = self.merge_source_data()

                if not merged_files:
                    self.log("数据合并失败，无法继续处理", "ERROR")
                    return
            else:
                self.log("跳过阶段0: 源数据合并")

            # 阶段1: 客服指标汇总表处理（如果勾选）
            if self.stage1_enabled.get():
                if self.stop_processing:
                    self.log("处理已被用户停止", "WARNING")
                    return

                if not merged_files:
                    if self.stage0_enabled.get():
                        self.log("阶段0未成功，无法执行阶段1", "ERROR")
                        return
                    else:
                        # 用户未勾选阶段0，尝试查找已存在的合并文件
                        self.log("未勾选阶段0，尝试查找已存在的合并文件...")
                        merged_files = self.find_existing_merged_files()
                        if not merged_files:
                            self.log("未找到已存在的合并文件，无法执行阶段1。请先勾选阶段0进行数据合并。", "ERROR")
                            return
                        else:
                            self.log(f"找到 {len(merged_files)} 个已存在的合并文件")

                self.log("=== 阶段1: 客服指标汇总表处理 ===")
                self.process_summary_file(merged_files)
            else:
                self.log("跳过阶段1: 客服指标汇总表处理")

            # 阶段2: 客服业绩预算表处理（如果勾选）
            if self.stage2_enabled.get():
                if self.stop_processing:
                    self.log("处理已被用户停止", "WARNING")
                    return

                if not merged_files:
                    if self.stage0_enabled.get() or self.stage1_enabled.get():
                        self.log("前置阶段未成功，无法执行阶段2", "ERROR")
                        return
                    else:
                        # 用户未勾选前置阶段，尝试查找已存在的合并文件
                        self.log("未勾选前置阶段，尝试查找已存在的合并文件...")
                        merged_files = self.find_existing_merged_files()
                        if not merged_files:
                            self.log("未找到已存在的合并文件，无法执行阶段2。请先勾选阶段0进行数据合并。", "ERROR")
                            return
                        else:
                            self.log(f"找到 {len(merged_files)} 个已存在的合并文件")

                self.log("=== 阶段2: 客服业绩预算表处理 ===")
                self.process_budget_file(merged_files)
            else:
                self.log("跳过阶段2: 客服业绩预算表处理")

            if self.stop_processing:
                self.log("处理已被用户停止", "WARNING")
                messagebox.showwarning("停止", "数据处理已被用户停止！")
            else:
                self.log("所有选定的处理阶段已完成！", "SUCCESS")
                messagebox.showinfo("完成", "数据处理已成功完成！")

        except Exception as e:
            self.log(f"处理过程中发生错误: {str(e)}", "ERROR")
            messagebox.showerror("错误", f"处理失败: {str(e)}")

        finally:
            # 恢复按钮状态
            self.process_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.is_processing = False
            self.stop_processing = False
            
    def standardize_shop_names(self):
        """执行店铺名标准化"""
        source_path = self.source_folder.get()
        standardizer = ShopNameStandardizer(self)
        return standardizer.standardize_shop_names(source_path)

    def merge_source_data(self):
        """执行源数据合并（阶段0）"""
        source_path = Path(self.source_folder.get())
        merger = DataMerger(source_path, self)
        return merger.run()

    def find_existing_merged_files(self):
        """查找已存在的合并文件"""
        source_path = Path(self.source_folder.get())
        merged_files = {}

        # 定义期望的合并文件名模式
        expected_patterns = [
            ('抖音_数据_客服数据.xlsx', 'DY_数据_客服数据'),
            ('拼多多_多多客服-客服数据-客服绩效数据-客服绩效详情.xlsx', 'PDD_多多客服-客服数据-客服绩效数据-客服绩效详情'),
            ('天猫_一目了然_新客服绩效数据.xlsx', 'TM_一目了然_新客服绩效数据'),
            ('京东_客服数据对比_工作量对比.xlsx', 'JD_客服数据对比_工作量对比'),
            ('京东_叮咚查询_促成订单查询.xlsx', 'JD_叮咚查询_促成订单查询'),
            ('天猫_绩效明细_旺旺销售明细.xlsx', 'TM_绩效明细_旺旺销售明细'),
            ('天猫_绩效明细_旺旺退款明细.xlsx', 'TM_绩效明细_旺旺退款明细')
        ]

        for filename, group_key in expected_patterns:
            file_path = source_path / filename
            if file_path.exists():
                merged_files[group_key] = file_path
                self.log(f"找到已存在的合并文件: {filename}")

        return merged_files
        
    def process_summary_file(self, merged_files):
        """处理客服指标汇总表（阶段1）"""
        processor = SummaryProcessor(self, merged_files)
        processor.process(self.summary_file.get())
        
    def process_budget_file(self, merged_files):
        """处理客服业绩预算表（阶段2）"""
        processor = BudgetProcessor(self, merged_files)
        processor.process(self.budget_file.get())
        
    def run(self):
        """运行应用程序"""
        self.root.mainloop()


class DataMerger:
    """数据合并器 - 改编自原data_merger.py"""
    
    def __init__(self, root_path, gui):
        """初始化数据合并器"""
        self.root_path = root_path
        self.gui = gui
        self.file_info = defaultdict(list)
        self.merged_files = {}
        
    def parse_filename(self, filename):
        """解析文件名，提取店铺、类型、时间信息"""
        name_without_ext = filename.replace('.xlsx', '').replace('.xls', '')
        
        # 匹配日期格式
        date_pattern = r'(\d{4}[-_]\d{2}[-_]\d{2})'
        date_match = re.search(date_pattern, name_without_ext)
        
        if date_match:
            date_part = date_match.group(1)
            parts_before_date = name_without_ext[:date_match.start()].rstrip('_-')
            parts = parts_before_date.split('_')
            
            if len(parts) >= 2:
                shop_name = parts[0]
                data_type = '_'.join(parts[1:])
            else:
                shop_name = parts[0] if parts else "未知店铺"
                data_type = "未知类型"
                
            return {
                'shop': shop_name,
                'type': data_type,
                'date': date_part,
                'original_filename': filename
            }
            
        # 处理其他格式
        parts = name_without_ext.split('_')
        if len(parts) >= 3:
            shop_name = parts[0]
            data_type = '_'.join(parts[1:-1])
            date_part = parts[-1]
        else:
            shop_name = name_without_ext
            data_type = "未知类型"
            date_part = "未知时间"
            
        return {
            'shop': shop_name,
            'type': data_type,
            'date': date_part,
            'original_filename': filename
        }
        
    def scan_files(self):
        """扫描所有Excel文件并分类"""
        self.gui.log("开始扫描文件...")
        
        for platform in ['DY', 'TM', 'JD', 'PDD']:
            platform_path = self.root_path / platform
            
            if not platform_path.exists():
                continue
                
            # 递归查找所有Excel文件
            for excel_file in platform_path.rglob("*.xls*"):
                if excel_file.is_file():
                    file_info = self.parse_filename(excel_file.name)
                    file_info['platform'] = platform
                    file_info['full_path'] = excel_file
                    
                    # 按平台_类型分组
                    group_key = f"{platform}_{file_info['type']}"
                    self.file_info[group_key].append(file_info)
                    
        total_files = sum(len(files) for files in self.file_info.values())
        self.gui.log(f"扫描完成，共发现 {total_files} 个文件，分为 {len(self.file_info)} 个合并组")
        
    def clean_and_convert_data(self, df):
        """清理数据：去除前后空格，转换数字类型"""
        if df.empty:
            return df
            
        df_cleaned = df.copy()
        
        # 清理字符串列
        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'object':
                df_cleaned[col] = df_cleaned[col].astype(str).str.strip()
                df_cleaned[col] = df_cleaned[col].replace('nan', None)
                
        # 转换数字列
        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'object':
                df_cleaned[col] = self.try_convert_to_numeric(df_cleaned[col])
                
        return df_cleaned
        
    def try_convert_to_numeric(self, series):
        """尝试将系列转换为数字类型"""
        if series.dtype != 'object':
            return series
            
        series_str = series.astype(str)
        has_percentage = series_str.str.contains('%', na=False).any()
        
        # 清理数据
        series_clean = series_str.str.strip()
        series_clean = series_clean.str.replace(',', '', regex=False)
        series_clean = series_clean.str.replace('，', '', regex=False)
        
        if has_percentage:
            numeric_part = series_clean.str.replace('%', '', regex=False)
            numeric_part = numeric_part.replace('nan', None)
            
            try:
                numeric_values = pd.to_numeric(numeric_part, errors='coerce')
                if numeric_values.notna().any():
                    return numeric_values / 100
            except:
                pass
        else:
            series_clean = series_clean.replace('nan', None)
            try:
                numeric_series = pd.to_numeric(series_clean, errors='coerce')
                if numeric_series.notna().any():
                    return numeric_series
            except:
                pass
                
        return series
        
    def read_excel_file(self, file_path):
        """读取Excel文件"""
        try:
            df = pd.read_excel(file_path, engine='calamine')
            
            if df.empty:
                return pd.DataFrame()
                
            # 清理列名
            df.columns = [
                str(col).strip() if pd.notna(col) else f"未命名列_{i}"
                for i, col in enumerate(df.columns)
            ]
            
            # 清理数据
            df = self.clean_and_convert_data(df)
            
            return df
            
        except Exception as e:
            self.gui.log(f"读取文件失败 {file_path}: {str(e)}", "ERROR")
            return pd.DataFrame()
            
    def convert_response_time_to_seconds(self, time_str):
        """将拼多多的响应时长转换为秒"""
        if pd.isna(time_str) or not isinstance(time_str, str):
            return 0
            
        try:
            match = re.search(r'(\d+)分(\d+)秒', str(time_str))
            if match:
                minutes = int(match.group(1))
                seconds = int(match.group(2))
                return minutes * 60 + seconds
            else:
                return 0
        except:
            return 0
            
    def clean_platform_data(self, df, platform):
        """根据平台清理数据，去掉汇总行"""
        if df.empty:
            return df
            
        df_cleaned = df.copy()
        
        if platform == 'PDD':
            # 拼多多：去掉汇总行
            if '客服账号' in df_cleaned.columns:
                mask = df_cleaned['客服账号'].astype(str).str.strip().str.contains(
                    '店铺总计|主账号', case=False, na=False
                )
                df_cleaned = df_cleaned[~mask]
                
            # 添加响应时长转换列
            if '平均人工响应时长' in df_cleaned.columns:
                df_cleaned['平均人工响应时长2'] = df_cleaned['平均人工响应时长'].apply(
                    self.convert_response_time_to_seconds
                )
                
        elif platform == 'JD':
            # 京东：去掉汇总行
            for col in df_cleaned.columns:
                if '客服' in col:
                    mask = df_cleaned[col].astype(str).str.strip().str.contains(
                        '总值|均值', case=False, na=False
                    )
                    df_cleaned = df_cleaned[~mask]
                    break
                    
        elif platform == 'DY':
            # 抖音：去掉汇总行
            if '客服昵称' in df_cleaned.columns:
                mask = df_cleaned['客服昵称'].astype(str).str.strip().str.contains(
                    '汇总|均值', case=False, na=False
                )
                df_cleaned = df_cleaned[~mask]
                
        elif platform == 'TM':
            # 天猫：去掉汇总行
            if '旺旺' in df_cleaned.columns:
                mask = df_cleaned['旺旺'].astype(str).str.strip().str.contains(
                    '汇总|均值', case=False, na=False
                )
                df_cleaned = df_cleaned[~mask]
                
        return df_cleaned
        
    def extract_year_month(self):
        """从根目录名称中提取年月格式"""
        for part in self.root_path.parts:
            date_match = re.search(r'(\d{4})-(\d{2})-\d{2}_\d{4}-\d{2}-\d{2}', part)
            if date_match:
                year, month = date_match.groups()
                return int(f"{year}{month}")
        return 202507  # 默认值
        
    def add_time_column(self, df):
        """在数据框第一列添加时间列"""
        if df.empty:
            return df
            
        time_value = self.extract_year_month()
        df_with_time = df.copy()
        df_with_time.insert(0, '时间', time_value)
        
        return df_with_time
        
    def merge_dataframes(self, dataframes, group_key):
        """合并多个数据框"""
        if not dataframes:
            return pd.DataFrame()
            
        if len(dataframes) == 1:
            return dataframes[0].copy()
            
        # 统一列结构
        base_columns = list(dataframes[0].columns)
        all_columns_set = set(base_columns)
        
        for df in dataframes[1:]:
            all_columns_set.update(df.columns)
            
        additional_columns = [col for col in all_columns_set if col not in base_columns]
        final_columns = base_columns + additional_columns
        
        # 重新索引并合并
        unified_dfs = []
        for df in dataframes:
            df_reindexed = df.reindex(columns=final_columns)
            unified_dfs.append(df_reindexed)
            
        merged_df = pd.concat(unified_dfs, ignore_index=True)
        # 处理"新平均响应时间"字段
        merged_df = self.handle_response_time_columns(merged_df)
        
        return merged_df
    
    def handle_response_time_columns(self, df):
        """
        处理响应时间列，将"新平均响应时间"合并到"平均响应时间"
        """
        if df.empty:
            return df

        df_result = df.copy()

        # 检查是否同时存在"平均响应时间"和"新平均响应时间"
        if '平均响应时间' in df_result.columns and '新平均响应时间' in df_result.columns:
            # 用"新平均响应时间"的非空值更新"平均响应时间"
            mask = df_result['新平均响应时间'].notna()
            df_result.loc[mask, '平均响应时间'] = df_result.loc[mask, '新平均响应时间']

            # 删除"新平均响应时间"列
            df_result = df_result.drop(columns=['新平均响应时间'])

        elif '新平均响应时间' in df_result.columns and '平均响应时间' not in df_result.columns:
            # 如果只有"新平均响应时间"，重命名为"平均响应时间"
            df_result = df_result.rename(columns={'新平均响应时间': '平均响应时间'})

        return df_result
    def process_group(self, group_key, file_list):
        """处理一个分组的文件合并"""
        try:
            dataframes = []
            platform = file_list[0]['platform']
            
            for file_info in file_list:
                df = self.read_excel_file(file_info['full_path'])
                if not df.empty:
                    df_cleaned = self.clean_platform_data(df, platform)
                    if not df_cleaned.empty:
                        dataframes.append(df_cleaned)
                        
            if not dataframes:
                return None
            
            # 如果数据中没有"店铺"列，则从文件名中提取店铺名称
            if '店铺' not in df.columns:
                shop_name = file_info['shop']
                df_with_shop = df.copy()
                df_with_shop.insert(0, '店铺', shop_name)
                
            # 合并数据
            merged_df = self.merge_dataframes(dataframes, group_key)
            
            if merged_df.empty:
                return None
                
            # 添加时间列
            merged_df = self.add_time_column(merged_df)
            
            # 处理京东特殊数据合并
            if group_key == 'JD_客服数据对比_工作量对比':
                merged_df = self.merge_jingdong_data(merged_df)
                
            # 生成输出文件名 - 使用中文平台名称
            platform_name = self.gui.platform_names[platform]
            data_type = file_list[0]['type']
            output_filename = f"{platform_name}_{data_type}.xlsx"
            
            # 保存到源文件夹（不创建子文件夹）
            output_path = self.root_path / output_filename
            
            # 保存Excel
            self.save_excel_with_formatting(merged_df, output_path)
            
            self.gui.log(f"已保存: {output_filename} ({len(merged_df)} 行数据)")
            
            return output_path
            
        except Exception as e:
            self.gui.log(f"处理分组 {group_key} 时发生错误: {str(e)}", "ERROR")
            return None
            
    def merge_jingdong_data(self, merged_df):
        """处理京东数据的特殊合并需求"""
        sales_group_key = 'JD_客服数据对比_售前销售绩效对比'
        if sales_group_key not in self.file_info:
            return merged_df
            
        try:
            # 读取售前销售数据
            sales_dataframes = []
            for file_info in self.file_info[sales_group_key]:
                df = self.read_excel_file(file_info['full_path'])
                if not df.empty:
                    df = self.clean_platform_data(df, 'JD')
                    
                    # 如果数据中没有"店铺"列，则从文件名中提取店铺名称
                    if '店铺' not in df.columns:
                        shop_name = file_info['shop']
                        df_with_shop = df.copy()
                        df_with_shop.insert(0, '店铺', shop_name)
                        
                    sales_dataframes.append(df)
                    
            if not sales_dataframes:
                return merged_df
                
            # 合并售前数据
            sales_df = self.merge_dataframes(sales_dataframes, sales_group_key)
            sales_df = self.add_time_column(sales_df)
            
            # 查找客服列
            work_customer_col = None
            sales_customer_col = None
            
            for col in merged_df.columns:
                if '客服' in col:
                    work_customer_col = col
                    break
                    
            for col in sales_df.columns:
                if '客服' in col:
                    sales_customer_col = col
                    break
                    
            if not work_customer_col or not sales_customer_col:
                return merged_df
                
            # 准备合并
            # 确保需要的列都存在
            required_columns = ['时间', '店铺', sales_customer_col, '售前接待人数', '促成出库人数']
            available_columns = [col for col in required_columns if col in sales_df.columns]
            sales_merge_df = sales_df[available_columns].copy()
            sales_merge_df = sales_merge_df.rename(columns={sales_customer_col: work_customer_col})
            
            # 确保合并所需的列都存在
            merge_on_columns = ['时间', '店铺', work_customer_col]
            available_merge_columns = [col for col in merge_on_columns if col in merged_df.columns and col in sales_merge_df.columns]
            
            if len(available_merge_columns) < 2:  # 至少需要时间和其他一列才能合并
                self.gui.log("合并所需的关键列不足，跳过京东数据合并", "WARNING")
                return merged_df
            
            # 执行合并
            result_df = merged_df.merge(
                sales_merge_df,
                on=available_merge_columns,
                how='left'
            )
            
            return result_df
            
        except Exception as e:
            self.gui.log(f"处理京东数据合并时发生错误: {str(e)}", "ERROR")
            return merged_df
            
    def save_excel_with_formatting(self, df, output_path):
        """保存Excel文件并设置格式"""
        wb = Workbook()
        ws = wb.active
        
        # 写入数据
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
            
        # 设置百分比格式
        for col_idx, col_name in enumerate(df.columns, 1):
            if df[col_name].dtype in ['float64', 'int64']:
                non_null_values = df[col_name].dropna()
                if len(non_null_values) > 0:
                    in_percent_range = ((non_null_values >= 0) & (non_null_values <= 1)).sum()
                    total_values = len(non_null_values)
                    
                    is_percentage_column = (
                        in_percent_range / total_values > 0.7 and
                        ('率' in col_name or '比' in col_name)
                    )
                    
                    if is_percentage_column:
                        for row_idx in range(2, len(df) + 2):
                            cell = ws.cell(row=row_idx, column=col_idx)
                            cell.number_format = '0.00%'
                            
        # 保存文件
        wb.save(output_path)
        
    def run(self):
        """运行合并流程"""
        # 检查停止标志
        if self.gui.stop_processing:
            self.gui.log("数据合并被用户停止", "WARNING")
            return {}

        # 扫描文件
        self.scan_files()

        if not self.file_info:
            self.gui.log("没有找到任何Excel文件", "WARNING")
            return {}

        # 设置进度条
        total_groups = len(self.file_info)
        self.gui.progress_queue.put(("max_progress", total_groups))

        # 处理每个分组
        progress = 0
        for group_key, file_list in self.file_info.items():
            # 检查停止标志
            if self.gui.stop_processing:
                self.gui.log("数据合并被用户停止", "WARNING")
                break

            output_path = self.process_group(group_key, file_list)
            if output_path:
                self.merged_files[group_key] = output_path

            progress += 1
            self.gui.progress_queue.put(("progress", progress))

        self.gui.log(f"数据合并完成，共生成 {len(self.merged_files)} 个文件")

        return self.merged_files


class SummaryProcessor:
    """客服指标汇总表处理器"""
    
    def __init__(self, gui, merged_files):
        self.gui = gui
        self.merged_files = merged_files
        
    def clean_zero_values(self, df):
        """清洗零值数据（阶段1特有）- 只针对指标汇总明细Sheet页"""
        df_cleaned = df.copy()

        # 定义需要清洗的零值格式
        zero_patterns = [
            '0', '0.0', '0.00', '0%', '0.00%',
            '-', '--', '无', '', ' ', '  '
        ]

        for col in df_cleaned.columns:
            if df_cleaned[col].dtype == 'object':
                # 先转换为字符串并去除前后空格
                df_cleaned[col] = df_cleaned[col].astype(str).str.strip()

                # 替换各种零值格式为空值
                for pattern in zero_patterns:
                    df_cleaned[col] = df_cleaned[col].replace(pattern, None)

                # 处理 'nan' 字符串
                df_cleaned[col] = df_cleaned[col].replace('nan', None)

            elif df_cleaned[col].dtype in ['float64', 'int64']:
                # 数字类型的零值也替换为空值
                df_cleaned[col] = df_cleaned[col].replace(0, None)
                df_cleaned[col] = df_cleaned[col].replace(0.0, None)
                df_cleaned[col] = df_cleaned[col].replace(0.00, None)

        return df_cleaned
        
    def process(self, summary_file_path):
        """处理客服指标汇总表"""
        try:
            # 打开Excel文件
            wb = load_workbook(summary_file_path)
            
            # 处理各平台数据
            for platform_cn, source_file in self.gui.stage1_file_mapping.items():
                self.gui.log(f"处理{platform_cn}数据...")
                
                # 查找对应的合并文件
                merged_file_path = None
                for key, path in self.merged_files.items():
                    if source_file in str(path):
                        merged_file_path = path
                        break
                        
                if not merged_file_path:
                    self.gui.log(f"未找到{platform_cn}的数据文件: {source_file}", "WARNING")
                    continue
                    
                # 读取数据
                df = pd.read_excel(merged_file_path)

                # 追加到对应的平台Sheet（不清洗零值）
                if platform_cn in wb.sheetnames:
                    self.append_to_sheet(wb, platform_cn, df)
                else:
                    self.gui.log(f"目标文件中不存在{platform_cn}工作表", "WARNING")
                    
            # 汇总到明细表
            self.gui.log("汇总数据到指标汇总明细表...")
            self.summarize_to_detail_sheet(wb)
            
            # 保存文件
            wb.save(summary_file_path)
            self.gui.log("客服指标汇总表处理完成")
            
        except Exception as e:
            self.gui.log(f"处理客服指标汇总表时发生错误: {str(e)}", "ERROR")
            
    def append_to_sheet(self, wb, sheet_name, df):
        """追加数据到指定Sheet"""
        ws = wb[sheet_name]
        
        # 获取当前Sheet的列名
        existing_columns = []
        for cell in ws[1]:
            if cell.value:
                existing_columns.append(str(cell.value).strip())
                
        # 只保留目标Sheet中存在的列
        df_filtered = df[[col for col in existing_columns if col in df.columns]]
        
        # 找到第一个完全空的行来追加数据
        last_row = 1  # 从标题行开始

        # 从第2行开始检查，找到第一个完全空的行
        for row_num in range(2, ws.max_row + 2):  # +2 确保能检查到最后一行之后
            # 检查这一行是否完全为空
            is_empty_row = True
            for col_num in range(1, len(existing_columns) + 1):
                cell_value = ws.cell(row=row_num, column=col_num).value
                if cell_value is not None and str(cell_value).strip():
                    is_empty_row = False
                    break

            if is_empty_row:
                # 找到第一个空行，从这里开始追加
                last_row = row_num - 1
                break
            else:
                # 这一行有数据，继续检查下一行
                last_row = row_num

        # 追加数据
        for _, row in df_filtered.iterrows():
            last_row += 1
            for col_idx, value in enumerate(row, 1):
                if pd.notna(value):
                    cell = ws.cell(row=last_row, column=col_idx, value=value)
                    # 设置字体为微软雅黑9号并居中
                    from openpyxl.styles import Font, Alignment
                    cell.font = Font(name='微软雅黑', size=9)
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    
        self.gui.log(f"已向{sheet_name}追加 {len(df_filtered)} 行数据")
        
    def summarize_to_detail_sheet(self, wb):
        """汇总数据到指标汇总明细表"""
        if '指标汇总明细' not in wb.sheetnames:
            self.gui.log("目标文件中不存在'指标汇总明细'工作表", "WARNING")
            return

        detail_ws = wb['指标汇总明细']

        # 获取明细表的列名
        target_columns = []
        for cell in detail_ws[1]:
            if cell.value:
                target_columns.append(str(cell.value).strip())

        self.gui.log(f"指标汇总明细表目标列: {target_columns}")

        # 收集所有平台的数据
        all_data = []

        # 平台映射：中文名 -> 英文代码
        platform_mapping = {
            '拼多多': 'PDD',
            '抖音': 'DY',
            '天猫': 'TM',
            '京东': 'JD'
        }

        for platform_cn, platform_code in platform_mapping.items():
            self.gui.log(f"处理{platform_cn}数据...")

            # 查找对应的合并文件
            source_file = self.gui.stage1_file_mapping.get(platform_cn)
            if not source_file:
                self.gui.log(f"未找到{platform_cn}的文件映射配置", "WARNING")
                continue

            merged_file_path = None
            for key, path in self.merged_files.items():
                if source_file in str(path):
                    merged_file_path = path
                    break

            if not merged_file_path or not os.path.exists(merged_file_path):
                self.gui.log(f"未找到{platform_cn}的合并文件: {source_file}", "WARNING")
                continue

            try:
                # 读取合并文件数据
                df = pd.read_excel(merged_file_path)
                self.gui.log(f"从{platform_cn}合并文件读取到 {len(df)} 行数据")

                # 按字段映射规则转换数据
                for _, row in df.iterrows():
                    platform_output = '拼多多平台' if platform_cn == '拼多多' else '非拼多多平台'
                    mapped_row = {'平台': platform_output}

                    # 按照字段映射规则映射每个字段
                    for target_field, source_mapping in self.gui.stage1_field_mapping.items():
                        source_field = source_mapping.get(platform_cn)
                        if source_field and source_field in df.columns:
                            value = row[source_field]
                            if pd.notna(value):
                                mapped_row[target_field] = value
                            else:
                                mapped_row[target_field] = None
                        else:
                            # 如果没有映射或源字段不存在，设为None
                            mapped_row[target_field] = None

                    all_data.append(mapped_row)

            except Exception as e:
                self.gui.log(f"读取{platform_cn}合并文件时出错: {str(e)}", "ERROR")
                continue

        if not all_data:
            self.gui.log("没有数据需要汇总到指标汇总明细表", "WARNING")
            return

        self.gui.log(f"共收集到 {len(all_data)} 行数据，开始汇总...")

        # 创建DataFrame并应用零值清洗
        df_all = pd.DataFrame(all_data)
        self.gui.log(f"清洗前数据行数: {len(df_all)}")

        # 应用零值清洗（只针对指标汇总明细表）
        df_all = self.clean_zero_values(df_all)
        self.gui.log(f"清洗后数据行数: {len(df_all)}")

        # 找到第一个完全空的行来追加数据（修复空行间隙问题）
        last_row = 1  # 从标题行开始

        # 从第2行开始检查，找到第一个完全空的行
        for row_num in range(2, detail_ws.max_row + 2):  # +2 确保能检查到最后一行之后
            # 检查这一行是否完全为空
            is_empty_row = True
            for col_num in range(1, len(target_columns) + 1):
                cell_value = detail_ws.cell(row=row_num, column=col_num).value
                if cell_value is not None and str(cell_value).strip():
                    is_empty_row = False
                    break

            if is_empty_row:
                # 找到第一个空行，从这里开始追加
                last_row = row_num - 1
                break
            else:
                # 这一行有数据，继续检查下一行
                last_row = row_num

        # 写入数据
        written_count = 0
        for _, row in df_all.iterrows():
            last_row += 1
            for col_idx, col_name in enumerate(target_columns, 1):
                if col_name in row:
                    value = row[col_name]
                    if pd.notna(value):
                        cell = detail_ws.cell(row=last_row, column=col_idx)

                        # 特殊处理满意率：转换为百分比格式
                        if col_name == '满意率':
                            try:
                                # 尝试转换为数字
                                if isinstance(value, str):
                                    # 如果是字符串形式的百分比（如"95%"），去掉%号
                                    if value.endswith('%'):
                                        numeric_value = float(value.rstrip('%')) / 100
                                    else:
                                        numeric_value = float(value)
                                elif isinstance(value, (int, float)):
                                    numeric_value = float(value)
                                else:
                                    # 无法转换的值，直接写入
                                    cell.value = value
                                    continue

                                # 如果是小数形式（如0.95），直接设置百分比格式
                                if 0 <= numeric_value <= 1:
                                    cell.value = numeric_value
                                    cell.number_format = '0.00%'
                                # 如果已经是百分比形式（如95），转换为小数并设置格式
                                elif numeric_value > 1:
                                    cell.value = numeric_value/100
                                    cell.number_format = '0.00%'
                                else:
                                    cell.value = numeric_value
                                    cell.number_format = '0.00%'
                            except (ValueError, TypeError):
                                # 转换失败，直接写入原值
                                cell.value = value
                        else:
                            cell.value = value

                        # 设置字体为微软雅黑9号并居中
                        from openpyxl.styles import Font, Alignment
                        cell.font = Font(name='微软雅黑', size=9)
                        cell.alignment = Alignment(horizontal='center', vertical='center')

            # 添加XLOOKUP公式到姓名、部门、岗位列
            self.add_lookup_formulas(detail_ws, last_row, target_columns)
            written_count += 1

        self.gui.log(f"已向指标汇总明细表追加 {written_count} 行数据")

    def add_lookup_formulas(self, detail_ws, row_num, target_columns):
        """添加XLOOKUP公式到姓名、部门、岗位列"""
        try:
            # 读取实际的列标题，确定正确的列位置
            actual_columns = []
            for col_idx in range(1, len(target_columns) + 1):
                cell_value = detail_ws.cell(row=1, column=col_idx).value
                if cell_value:
                    actual_columns.append((col_idx, str(cell_value).strip()))

            # 查找店铺、客服昵称、姓名、部门、岗位列的实际位置
            shop_col = None
            service_name_col = None
            name_col = None
            dept_col = None
            position_col = None

            for col_idx, col_name in actual_columns:
                if col_name == '店铺':
                    shop_col = col_idx
                elif col_name == '客服昵称':
                    service_name_col = col_idx
                elif col_name == '姓名':
                    name_col = col_idx
                elif col_name == '部门':
                    dept_col = col_idx
                elif col_name == '岗位':
                    position_col = col_idx

            # 如果找到了店铺和客服昵称列，添加公式
            if shop_col and service_name_col:
                # 获取列字母
                shop_letter = self.get_column_letter(shop_col)
                service_name_letter = self.get_column_letter(service_name_col)

                # 姓名公式
                if name_col:
                    name_cell_address = f"{self.get_column_letter(name_col)}{row_num}"
                    formula = ArrayFormula(name_cell_address, f'=XLOOKUP(${shop_letter}{row_num}&${service_name_letter}{row_num},客服花名册!$A:$A&客服花名册!$B:$B,客服花名册!$D:$D,"")')
                    cell = detail_ws.cell(row=row_num, column=name_col)
                    cell.value = formula
                    cell.font = Font(name='微软雅黑', size=9)
                    cell.alignment = Alignment(horizontal='center', vertical='center')

                # 部门公式
                if dept_col:
                    dept_cell_address = f"{self.get_column_letter(dept_col)}{row_num}"
                    formula = ArrayFormula(dept_cell_address, f'=XLOOKUP(${shop_letter}{row_num}&${service_name_letter}{row_num},客服花名册!$A:$A&客服花名册!$B:$B,客服花名册!$F:$F,"")')
                    cell = detail_ws.cell(row=row_num, column=dept_col)
                    cell.value = formula
                    cell.font = Font(name='微软雅黑', size=9)
                    cell.alignment = Alignment(horizontal='center', vertical='center')

                # 岗位公式
                if position_col:
                    position_cell_address = f"{self.get_column_letter(position_col)}{row_num}"
                    formula = ArrayFormula(position_cell_address, f'=XLOOKUP(${shop_letter}{row_num}&${service_name_letter}{row_num},客服花名册!$A:$A&客服花名册!$B:$B,客服花名册!$E:$E,"")')
                    cell = detail_ws.cell(row=row_num, column=position_col)
                    cell.value = formula
                    cell.font = Font(name='微软雅黑', size=9)
                    cell.alignment = Alignment(horizontal='center', vertical='center')

        except Exception as e:
            self.gui.log(f"添加XLOOKUP公式时出错: {str(e)}", "WARNING")

    def get_column_letter(self, col_num):
        """将列号转换为Excel列字母"""
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result


class BudgetProcessor:
    """客服业绩预算表处理器"""
    
    def __init__(self, gui, merged_files):
        self.gui = gui
        self.merged_files = merged_files
        


    def recreate_workbook_from_pandas(self, budget_file_path):
        """使用pandas读取Excel文件并重新创建工作簿"""
        try:
            from openpyxl import Workbook

            self.gui.log("data_only模式也失败，尝试使用pandas读取后重新创建", "WARNING")

            # 使用pandas读取所有工作表
            try:
                excel_file = pd.ExcelFile(budget_file_path, engine='openpyxl')
                sheet_names = excel_file.sheet_names
                self.gui.log(f"成功读取Excel文件，包含工作表: {sheet_names}", "INFO")
            except Exception as e:
                self.gui.log(f"pandas读取Excel文件也失败: {str(e)}", "ERROR")
                # 尝试使用xlrd引擎
                try:
                    excel_file = pd.ExcelFile(budget_file_path, engine='xlrd')
                    sheet_names = excel_file.sheet_names
                    self.gui.log(f"使用xlrd引擎成功读取Excel文件，包含工作表: {sheet_names}", "INFO")
                except Exception as e2:
                    self.gui.log(f"xlrd引擎也失败: {str(e2)}", "ERROR")
                    return None

            # 创建新的工作簿
            wb = Workbook()

            # 删除默认工作表
            if 'Sheet' in wb.sheetnames:
                wb.remove(wb['Sheet'])

            # 重新创建每个工作表
            for sheet_name in sheet_names:
                try:
                    self.gui.log(f"正在重新创建工作表: {sheet_name}", "INFO")

                    # 读取工作表数据
                    df = pd.read_excel(excel_file, sheet_name=sheet_name)
                    ws = wb.create_sheet(sheet_name)

                    # 写入列标题
                    if not df.empty:
                        for c_idx, col_name in enumerate(df.columns, 1):
                            ws.cell(row=1, column=c_idx, value=str(col_name))

                        # 写入数据
                        for r_idx, (_, row) in enumerate(df.iterrows(), 2):
                            for c_idx, value in enumerate(row, 1):
                                if pd.notna(value):
                                    ws.cell(row=r_idx, column=c_idx, value=value)
                    else:
                        # 空工作表，至少创建一个标题行
                        ws.cell(row=1, column=1, value="空工作表")

                except Exception as e:
                    self.gui.log(f"重新创建工作表 {sheet_name} 时出错: {str(e)}", "WARNING")
                    # 创建空工作表
                    ws = wb.create_sheet(sheet_name)
                    ws.cell(row=1, column=1, value="数据读取失败")

            excel_file.close()
            self.gui.log("成功使用pandas重新创建工作簿", "INFO")
            return wb

        except Exception as e:
            self.gui.log(f"使用pandas重新创建工作簿失败: {str(e)}", "ERROR")
            return None

    def process(self, budget_file_path):
        """处理客服业绩预算表"""
        try:
            # 打开Excel文件 - 使用多种方式尝试打开
            wb = None
            try:
                # 首先尝试正常打开
                wb = load_workbook(budget_file_path)
            except (ValueError, TypeError) as e:
                if "expected <class 'float'>" in str(e):
                    self.gui.log("Excel文件包含格式问题，尝试使用data_only模式打开", "WARNING")
                    try:
                        wb = load_workbook(budget_file_path, data_only=True)
                    except Exception as e2:
                        self.gui.log("data_only模式也失败，尝试使用pandas读取后重新创建", "WARNING")
                        # 使用pandas读取并重新创建工作簿
                        wb = self.recreate_workbook_from_pandas(budget_file_path)
                else:
                    raise e

            if wb is None:
                raise Exception("无法打开Excel文件")
            
            # 处理各平台数据
            try:
                self.process_pdd_dy(wb)
            except Exception as e:
                self.gui.log(f"处理拼多多/抖音数据时出错: {str(e)}", "ERROR")
                
            try:
                self.process_jd(wb)
            except Exception as e:
                self.gui.log(f"处理京东数据时出错: {str(e)}", "ERROR")
                
            try:
                self.process_tm(wb)
            except Exception as e:
                self.gui.log(f"处理天猫数据时出错: {str(e)}", "ERROR")
            
            # 保存文件
            try:
                wb.save(budget_file_path)
                self.gui.log("客服业绩预算表处理完成")
            except Exception as e:
                self.gui.log(f"保存文件时出错: {str(e)}", "ERROR")
                # 尝试另存为新文件
                try:
                    new_path = budget_file_path.replace('.xlsx', '_new.xlsx')
                    wb.save(new_path)
                    self.gui.log(f"已另存为: {new_path}", "WARNING")
                except Exception as e2:
                    self.gui.log(f"另存为也失败: {str(e2)}", "ERROR")
            
        except Exception as e:
            self.gui.log(f"处理客服业绩预算表时发生错误: {str(e)}", "ERROR")
            
    def clear_sheet_data(self, ws):
        """清空工作表数据（保留标题行）"""
        # 删除第2行及以后的所有数据
        if ws.max_row > 1:
            ws.delete_rows(2, ws.max_row - 1)

    def safe_value_convert(self, value):
        """安全地转换数据值，避免Excel写入错误"""
        if pd.isna(value):
            return None
        elif isinstance(value, (pd.Series, list)):
            # 如果是Series或list，取第一个值
            if hasattr(value, 'iloc') and len(value) > 0:
                return self.safe_value_convert(value.iloc[0])
            elif isinstance(value, list) and len(value) > 0:
                return self.safe_value_convert(value[0])
            else:
                return None
        elif hasattr(value, 'dtype') and 'object' in str(value.dtype):
            # 处理pandas object类型
            return str(value) if value is not None else None
        elif isinstance(value, (str, int, float, bool)):
            return value
        else:
            # 其他类型转换为字符串
            try:
                return str(value) if value is not None else None
            except:
                return None
        
    def process_pdd_dy(self, wb):
        """处理拼多多和抖音数据"""
        for platform in ['拼多多', '抖音']:
            if platform not in wb.sheetnames:
                continue
                
            self.gui.log(f"处理{platform}数据...")
            
            # 查找数据文件
            source_file = self.gui.stage2_file_mapping[platform]
            merged_file_path = None
            
            for key, path in self.merged_files.items():
                if source_file in str(path):
                    merged_file_path = path
                    break
                    
            if not merged_file_path:
                self.gui.log(f"未找到{platform}的数据文件", "WARNING")
                continue
                
            # 读取数据
            df = pd.read_excel(merged_file_path)
            
            # 清空目标Sheet
            ws = wb[platform]
            self.clear_sheet_data(ws)
            
            # 获取目标列
            target_columns = []
            for cell in ws[1]:
                if cell.value:
                    target_columns.append(str(cell.value).strip())
                    
            # 只保留目标列
            df_filtered = df[[col for col in target_columns if col in df.columns]]
            
            # 写入数据
            for _, row in df_filtered.iterrows():
                values = []
                for col in target_columns:
                    if col in df_filtered.columns:
                        value = self.safe_value_convert(row[col])
                    else:
                        value = None
                    values.append(value)
                ws.append(values)
                
            self.gui.log(f"已向{platform}写入 {len(df_filtered)} 行数据")
            
    def process_jd(self, wb):
        """处理京东数据"""
        self.gui.log("处理京东数据...")
        
        # 查找数据文件
        source_file = self.gui.stage2_file_mapping['京东']
        merged_file_path = None
        
        for key, path in self.merged_files.items():
            if source_file in str(path):
                merged_file_path = path
                break
                
        if not merged_file_path:
            self.gui.log("未找到京东的数据文件", "WARNING")
            return
            
        # 读取数据
        df = pd.read_excel(merged_file_path)
        
        # 筛选已出库和已完成的订单
        if '订单状态' in df.columns:
            df = df[df['订单状态'].isin(['已出库', '已完成'])]
            
        # 分类处理
        df_flagship = df[~df['店铺'].str.contains('自营店', na=False)]
        df_self = df[df['店铺'].str.contains('自营店', na=False)]
        
        # 处理旗舰店数据
        if '京东旗舰店' in wb.sheetnames and not df_flagship.empty:
            ws = wb['京东旗舰店']
            self.clear_sheet_data(ws)
            
            target_columns = []
            for cell in ws[1]:
                if cell.value:
                    target_columns.append(str(cell.value).strip())
                    
            df_filtered = df_flagship[[col for col in target_columns if col in df_flagship.columns]]
            
            for _, row in df_filtered.iterrows():
                values = []
                for col in target_columns:
                    if col in df_filtered.columns:
                        value = self.safe_value_convert(row[col])
                    else:
                        value = None
                    values.append(value)
                ws.append(values)
                
            self.gui.log(f"已向京东旗舰店写入 {len(df_filtered)} 行数据")
            
        # 处理自营店数据
        if '京东自营店' in wb.sheetnames and not df_self.empty:
            ws = wb['京东自营店']
            self.clear_sheet_data(ws)
            
            target_columns = []
            for cell in ws[1]:
                if cell.value:
                    target_columns.append(str(cell.value).strip())
                    
            df_filtered = df_self[[col for col in target_columns if col in df_self.columns]]
            
            for _, row in df_filtered.iterrows():
                values = []
                for col in target_columns:
                    if col in df_filtered.columns:
                        value = self.safe_value_convert(row[col])
                    else:
                        value = None
                    values.append(value)
                ws.append(values)
                
            self.gui.log(f"已向京东自营店写入 {len(df_filtered)} 行数据")
            
    def process_tm(self, wb):
        """处理天猫数据"""
        # 处理销售明细
        if '天猫旺旺销售明细' in wb.sheetnames:
            self.gui.log("处理天猫销售明细数据...")
            
            source_file = self.gui.stage2_file_mapping['天猫销售']
            merged_file_path = None
            
            for key, path in self.merged_files.items():
                if source_file in str(path):
                    merged_file_path = path
                    break
                    
            if merged_file_path:
                df = pd.read_excel(merged_file_path)
                ws = wb['天猫旺旺销售明细']
                self.clear_sheet_data(ws)
                
                target_columns = []
                for cell in ws[1]:
                    if cell.value:
                        target_columns.append(str(cell.value).strip())

                # 安全地筛选列，避免Series布尔运算错误
                available_columns = []
                df_columns_list = list(df.columns)  # 转换为列表避免Series问题
                for col in target_columns:
                    try:
                        if col in df_columns_list:
                            available_columns.append(col)
                    except Exception as e:
                        self.gui.log(f"检查列 {col} 时出错: {str(e)}", "WARNING")

                if available_columns:
                    try:
                        df_filtered = df[available_columns].copy()
                    except Exception as e:
                        self.gui.log(f"筛选列时出错: {str(e)}，尝试逐列处理", "WARNING")
                        # 逐列处理，避免复杂的列选择问题
                        df_filtered = pd.DataFrame()
                        for col in available_columns:
                            try:
                                df_filtered[col] = df[col]
                            except Exception as col_e:
                                self.gui.log(f"处理列 {col} 时出错: {str(col_e)}", "WARNING")
                else:
                    df_filtered = pd.DataFrame()
                
                # 批量处理数据，避免逐行append导致的性能问题
                if not df_filtered.empty:
                    self.gui.log(f"开始处理 {len(df_filtered)} 行天猫销售明细数据...")

                    # 批量转换数据
                    batch_data = []
                    df_filtered_columns = list(df_filtered.columns)

                    # 分批处理，避免内存问题
                    batch_size = 1000
                    total_rows = len(df_filtered)

                    for start_idx in range(0, total_rows, batch_size):
                        end_idx = min(start_idx + batch_size, total_rows)
                        batch_df = df_filtered.iloc[start_idx:end_idx]

                        # 检查是否需要停止
                        if self.gui.stop_processing:
                            self.gui.log("天猫销售明细处理被用户停止", "WARNING")
                            break

                        self.gui.log(f"处理第 {start_idx+1}-{end_idx} 行数据...")

                        for _, row in batch_df.iterrows():
                            values = []
                            for col in target_columns:
                                try:
                                    if col in df_filtered_columns:
                                        value = self.safe_value_convert(row[col])
                                    else:
                                        value = None
                                except Exception as e:
                                    value = None
                                values.append(value)
                            batch_data.append(values)

                    # 批量写入Excel
                    if batch_data and not self.gui.stop_processing:
                        self.gui.log(f"批量写入 {len(batch_data)} 行数据到Excel...")
                        for values in batch_data:
                            ws.append(values)

                        self.gui.log(f"已向天猫旺旺销售明细写入 {len(batch_data)} 行数据")
                    else:
                        self.gui.log("天猫销售明细数据为空或处理被停止")
                else:
                    self.gui.log("天猫销售明细数据为空")
                
        # 处理退款明细
        if '天猫旺旺退款明细' in wb.sheetnames:
            self.gui.log("处理天猫退款明细数据...")
            
            source_file = self.gui.stage2_file_mapping['天猫退款']
            merged_file_path = None
            
            for key, path in self.merged_files.items():
                if source_file in str(path):
                    merged_file_path = path
                    break
                    
            if merged_file_path:
                df = pd.read_excel(merged_file_path)
                ws = wb['天猫旺旺退款明细']
                self.clear_sheet_data(ws)
                
                target_columns = []
                for cell in ws[1]:
                    if cell.value:
                        target_columns.append(str(cell.value).strip())

                # 安全地筛选列，避免Series布尔运算错误
                available_columns = []
                df_columns_list = list(df.columns)  # 转换为列表避免Series问题
                for col in target_columns:
                    try:
                        if col in df_columns_list:
                            available_columns.append(col)
                    except Exception as e:
                        self.gui.log(f"检查列 {col} 时出错: {str(e)}", "WARNING")

                if available_columns:
                    try:
                        df_filtered = df[available_columns].copy()
                    except Exception as e:
                        self.gui.log(f"筛选列时出错: {str(e)}，尝试逐列处理", "WARNING")
                        # 逐列处理，避免复杂的列选择问题
                        df_filtered = pd.DataFrame()
                        for col in available_columns:
                            try:
                                df_filtered[col] = df[col]
                            except Exception as col_e:
                                self.gui.log(f"处理列 {col} 时出错: {str(col_e)}", "WARNING")
                else:
                    df_filtered = pd.DataFrame()
                
                # 批量处理数据，避免逐行append导致的性能问题
                if not df_filtered.empty:
                    self.gui.log(f"开始处理 {len(df_filtered)} 行天猫退款明细数据...")

                    # 批量转换数据
                    batch_data = []
                    df_filtered_columns = list(df_filtered.columns)

                    # 分批处理，避免内存问题
                    batch_size = 1000
                    total_rows = len(df_filtered)

                    for start_idx in range(0, total_rows, batch_size):
                        end_idx = min(start_idx + batch_size, total_rows)
                        batch_df = df_filtered.iloc[start_idx:end_idx]

                        # 检查是否需要停止
                        if self.gui.stop_processing:
                            self.gui.log("天猫退款明细处理被用户停止", "WARNING")
                            break

                        self.gui.log(f"处理第 {start_idx+1}-{end_idx} 行数据...")

                        for _, row in batch_df.iterrows():
                            values = []
                            for col in target_columns:
                                try:
                                    if col in df_filtered_columns:
                                        value = self.safe_value_convert(row[col])
                                    else:
                                        value = None
                                except Exception as e:
                                    value = None
                                values.append(value)
                            batch_data.append(values)

                    # 批量写入Excel
                    if batch_data and not self.gui.stop_processing:
                        self.gui.log(f"批量写入 {len(batch_data)} 行数据到Excel...")
                        for values in batch_data:
                            ws.append(values)

                        self.gui.log(f"已向天猫旺旺退款明细写入 {len(batch_data)} 行数据")
                    else:
                        self.gui.log("天猫退款明细数据为空或处理被停止")
                else:
                    self.gui.log("天猫退款明细数据为空")


class ShopNameStandardizer:
    """店铺名标准化处理器"""

    def __init__(self, gui):
        self.gui = gui

    def standardize_shop_names(self, source_path):
        """标准化店铺名称"""
        try:
            self.gui.log("开始店铺名标准化处理...")
            source_path = Path(source_path)

            if not source_path.exists():
                self.gui.log(f"源数据文件夹不存在: {source_path}", "ERROR")
                return False

            # 处理每个平台
            for platform in ['DY', 'TM', 'JD', 'PDD']:
                if self.gui.stop_processing:
                    self.gui.log("店铺名标准化被用户停止", "WARNING")
                    return False

                platform_path = source_path / platform
                if not platform_path.exists():
                    self.gui.log(f"平台文件夹不存在: {platform}", "WARNING")
                    continue

                self.gui.log(f"处理{platform}平台店铺名...")
                self.process_platform_shops(platform_path, platform)

            self.gui.log("店铺名标准化处理完成", "SUCCESS")
            return True

        except Exception as e:
            self.gui.log(f"店铺名标准化处理失败: {str(e)}", "ERROR")
            return False

    def process_platform_shops(self, platform_path, platform):
        """处理单个平台的店铺文件夹"""
        try:
            # 查找时间段文件夹（如2025-06-01_2025-06-30）
            time_folders = [f for f in platform_path.iterdir() if f.is_dir() and re.match(r'\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}', f.name)]

            if not time_folders:
                self.gui.log(f"{platform}平台下未找到时间段文件夹", "WARNING")
                return

            for time_folder in time_folders:
                self.gui.log(f"处理时间段文件夹: {time_folder.name}")

                # 获取店铺文件夹列表
                shop_folders = [f for f in time_folder.iterdir() if f.is_dir()]

                for shop_folder in shop_folders:
                    if self.gui.stop_processing:
                        return

                    self.process_shop_folder(shop_folder, platform)

        except Exception as e:
            self.gui.log(f"处理{platform}平台时出错: {str(e)}", "ERROR")

    def process_shop_folder(self, shop_folder, platform):
        """处理单个店铺文件夹"""
        try:
            original_shop_name = shop_folder.name
            self.gui.log(f"处理店铺: {original_shop_name}")

            # 获取标准化的店铺名
            standardized_name = self.get_standardized_shop_name(original_shop_name, platform)

            if standardized_name and standardized_name != original_shop_name:
                # 重命名文件夹
                new_folder_path = shop_folder.parent / standardized_name
                if not new_folder_path.exists():
                    shop_folder.rename(new_folder_path)
                    self.gui.log(f"文件夹重命名: {original_shop_name} -> {standardized_name}")
                    shop_folder = new_folder_path
                else:
                    self.gui.log(f"目标文件夹已存在，跳过重命名: {standardized_name}", "WARNING")
            else:
                standardized_name = original_shop_name

            # 处理文件夹内的Excel文件
            self.process_excel_files_in_folder(shop_folder, standardized_name)

        except Exception as e:
            self.gui.log(f"处理店铺文件夹 {shop_folder.name} 时出错: {str(e)}", "ERROR")

    def get_standardized_shop_name(self, original_name, platform):
        """获取标准化的店铺名"""
        if platform not in self.gui.shop_name_mapping:
            return original_name

        platform_mapping = self.gui.shop_name_mapping[platform]

        # 精确匹配
        if original_name in platform_mapping:
            return platform_mapping[original_name]

        # 模糊匹配（去除括号和空格）
        normalized_original = self.normalize_shop_name(original_name)

        for original_key, standardized_value in platform_mapping.items():
            normalized_key = self.normalize_shop_name(original_key)
            if normalized_original == normalized_key:
                return standardized_value

        return original_name

    def normalize_shop_name(self, name):
        """标准化店铺名用于比较（去除括号和空格）"""
        # 去除中英文括号和空格
        normalized = re.sub(r'[（）()\s]', '', name)
        return normalized.lower()

    def process_excel_files_in_folder(self, folder_path, shop_name):
        """处理文件夹内的Excel文件，添加店铺名前缀"""
        try:
            excel_files = list(folder_path.glob("*.xls*"))

            for excel_file in excel_files:
                if self.gui.stop_processing:
                    return

                self.add_shop_prefix_to_file(excel_file, shop_name)

        except Exception as e:
            self.gui.log(f"处理文件夹 {folder_path.name} 内的Excel文件时出错: {str(e)}", "ERROR")

    def add_shop_prefix_to_file(self, file_path, shop_name):
        """为文件添加店铺名前缀"""
        try:
            original_filename = file_path.name

            # 检查文件名是否已经包含店铺名前缀
            if self.file_already_has_shop_prefix(original_filename, shop_name):
                self.gui.log(f"文件已包含店铺名前缀，跳过: {original_filename}")
                return

            # 生成新的文件名
            new_filename = f"{shop_name}_{original_filename}"
            new_file_path = file_path.parent / new_filename

            # 检查新文件名是否已存在
            if new_file_path.exists():
                self.gui.log(f"目标文件已存在，跳过重命名: {new_filename}", "WARNING")
                return

            # 重命名文件
            file_path.rename(new_file_path)
            self.gui.log(f"文件重命名: {original_filename} -> {new_filename}")

        except Exception as e:
            self.gui.log(f"重命名文件 {file_path.name} 时出错: {str(e)}", "ERROR")

    def file_already_has_shop_prefix(self, filename, shop_name):
        """检查文件是否已经包含店铺名前缀"""
        # 标准化文件名和店铺名用于比较
        normalized_filename = self.normalize_shop_name(filename)
        normalized_shop_name = self.normalize_shop_name(shop_name)

        # 检查文件名是否以店铺名开头
        return normalized_filename.startswith(normalized_shop_name)


if __name__ == "__main__":
    app = DataSummarySystem()
    app.run()